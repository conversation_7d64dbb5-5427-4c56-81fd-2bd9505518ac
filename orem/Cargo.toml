[package]
name = "orem"
version = "1.0.10"
edition = "2021"

[profile.release]
opt-level = 3        # 最高级别的优化
lto = true           # 启用链接时间优化
codegen-units = 1    # 单一代码生成单元以提高优化效果
debug = false        # 不包含调试信息
rpath = false        # 禁用运行时搜索路径
panic = 'abort'      # 使用 abort 而不是 unwind 的 panic 策略
incremental = false  # 禁用增量编译

[dependencies]
# WebSocket 客户端依赖 - 用于与服务端进行实时通信
tokio-tungstenite = { version = "0.26.2", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3.31"
# HTTP 客户端依赖 - 仅用于软件包文件下载，不用于API通信
reqwest = { version = "0.12.19", default-features = false, features = ["blocking", "json", "rustls-tls"] }
# 其他依赖
colored = "3.0.0"
regex = "1.11.1"
base64 = "0.22.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
uuid = { version = "1.17.0", features = ["v4"] }
nix = { version = "0.30.1", features = ["user"] }
dialoguer = "0.11.0"
indicatif = "0.17.11"
chrono = "0.4.41"
log = "0.4.27"
flexi_logger = "0.30.2"
tokio = { version = "1.45.1", features = ["full"] }
rsntp = "4.0.0"
thiserror = "2.0.12"
once_cell = "1.21.3"
url = "2.5.4"
sysinfo = "0.35.2"
tabled = { version = "0.20.0", features = ["std", "derive", "ansi"] }
validator = { version = "0.20.0", features = ["derive"] }
config = "0.15.11"