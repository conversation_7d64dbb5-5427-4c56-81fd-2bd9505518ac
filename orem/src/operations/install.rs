use crate::{
    basic::config::types::Settings,
    file::{create_directories, set_permissions},
    oren::add::{
        add_oren_service, done_oren_service, init_oren_service, monitor_log_file,
        start_oren_service,
    },
    oren::config::{oren_config_ini, oren_nginx_conf},
    service::{enable_services, start_orebs_service, start_services, stop_services},
    software::download,
    user::{check_and_remove_user_group, create_user_group},
};

use colored::Colorize;
use dialoguer::{theme::ColorfulTheme, Confirm};
use log::{error, info};
use std::sync::Arc;
use thiserror::Error;
use tokio::{
    runtime::Runtime,
    time::{sleep, Duration},
};

/// 安装操作相关错误
#[derive(Error, Debug)]
pub enum InstallError {
    /// 用户组操作错误
    #[error("用户组操作错误: {0}")]
    UserGroupError(String),

    /// 目录创建错误
    #[error("目录创建错误: {0}")]
    DirectoryError(String),

    /// 权限设置错误
    #[error("权限设置错误: {0}")]
    PermissionError(String),

    /// 软件下载错误
    #[error("软件下载错误: {0}")]
    DownloadError(String),

    /// 服务配置错误
    #[error("服务配置错误: {0}")]
    ServiceConfigError(String),

    /// 服务操作错误
    #[error("服务操作错误: {0}")]
    ServiceOperationError(String),

    /// 异步运行时错误
    #[error("异步运行时错误: {0}")]
    AsyncRuntimeError(#[from] tokio::io::Error),

    /// OREN 操作错误
    #[error("OREN 操作错误: {0}")]
    OrenOperationError(String),

    /// 用户交互错误
    #[error("用户交互错误: {0}")]
    InteractionError(String),
}

/// 定义 Result 类型别名
pub type Result<T> = std::result::Result<T, InstallError>;

/// 用户确认操作函数
fn confirm_action(prompt_message: &str) -> Result<bool> {
    Confirm::with_theme(&ColorfulTheme::default())
        .with_prompt(prompt_message)
        .default(true)
        .interact()
        .map_err(|e| {
            let err_msg = format!("用户交互错误: {}", e);
            error!("{}", err_msg);
            InstallError::InteractionError(err_msg)
        })
}

/// 安装 OpenResty Edge Admin
pub fn install_orea(settings: Arc<Settings>) -> Result<()> {
    info!("开始安装...");
    println!("{}", "❚ 正在安装...".yellow().bold());

    // 检查并移除用户和组
    check_and_remove_user_group(1000)
        .map_err(|e| InstallError::UserGroupError(format!("移除用户组失败: {}", e)))?;
    check_and_remove_user_group(1001)
        .map_err(|e| InstallError::UserGroupError(format!("移除用户组失败: {}", e)))?;

    // 创建用户和组
    create_user_group("orea")
        .map_err(|e| InstallError::UserGroupError(format!("创建用户组失败: {}", e)))?;

    // 创建相关目录
    create_directories("orea")
        .map_err(|e| InstallError::DirectoryError(format!("创建目录失败: {}", e)))?;

    // 设置权限
    set_permissions().map_err(|e| InstallError::PermissionError(format!("设置权限失败: {}", e)))?;

    // 下载包并执行相关命令 - 传入 settings 参数
    download("orea", settings.clone())
        .map_err(|e| InstallError::DownloadError(format!("下载包失败: {}", e)))?;

    // 启用服务
    enable_services("orea")
        .map_err(|e| InstallError::ServiceOperationError(format!("启用服务失败: {}", e)))?;

    // 询问用户是否需要启动服务
    let prompt_message = "是否立即启动服务?";
    let confirm = confirm_action(&prompt_message.yellow().to_string())?;

    if confirm {
        info!("用户选择启动服务");
        // 启动服务
        start_services("orea")
            .map_err(|e| InstallError::ServiceOperationError(format!("启动服务失败: {}", e)))?;
        start_orebs_service().map_err(|e| {
            InstallError::ServiceOperationError(format!("启动 orebs 服务失败: {}", e))
        })?;
    } else {
        info!("用户选择不启动服务");
    }

    // 打印安装成功的消息
    info!("安装成功!");
    println!("{}", "安装成功! Enjoy! 🥳".green());

    Ok(())
}

/// 安装 OpenResty Edge Node
pub fn install_oren(settings: Arc<Settings>) -> Result<()> {
    info!("开始安装...");

    // 创建一个新的 Tokio 运行时
    let rt = Runtime::new().map_err(|e| InstallError::AsyncRuntimeError(e))?;

    println!("{}", "❚ 正在安装...".yellow().bold());

    // 检查并移除用户和组
    check_and_remove_user_group(1000)
        .map_err(|e| InstallError::UserGroupError(format!("移除用户组失败: {}", e)))?;

    // 创建用户和组
    create_user_group("oren")
        .map_err(|e| InstallError::UserGroupError(format!("创建用户组失败: {}", e)))?;

    // 创建相关目录
    create_directories("oren")
        .map_err(|e| InstallError::DirectoryError(format!("创建目录失败: {}", e)))?;

    // 设置权限
    set_permissions().map_err(|e| InstallError::PermissionError(format!("设置权限失败: {}", e)))?;

    // 下载包并执行相关命令 - 传入 settings 参数
    download("oren", settings.clone())
        .map_err(|e| InstallError::DownloadError(format!("下载包失败: {}", e)))?;

    // 调用 oren_config_ini 和 oren_nginx_conf 函数
    oren_config_ini(settings.clone())
        .map_err(|e| InstallError::ServiceConfigError(format!("配置 ini 文件失败: {}", e)))?;
    oren_nginx_conf(settings.clone())
        .map_err(|e| InstallError::ServiceConfigError(format!("配置 conf 文件失败: {}", e)))?;

    // 启用服务
    enable_services("oren")
        .map_err(|e| InstallError::ServiceOperationError(format!("启用服务失败: {}", e)))?;

    // 初始化配置并启动服务
    info!("开始初始化...");

    // 创建 settings 的引用, 以便在 async 块中使用
    let settings_ref = &settings;

    rt.block_on(async {
        // 初始化 OpenResty Edge Node - 传递 settings 参数
        init_oren_service(settings_ref)
            .await
            .map_err(|e| InstallError::OrenOperationError(format!("初始化服务失败: {}", e)))?;

        // 监听日志文件
        monitor_log_file()
            .await
            .map_err(|e| InstallError::OrenOperationError(format!("监听日志文件失败: {}", e)))?;

        // 添加 OpenResty Edge Node - 传递 settings 参数
        // WebSocket 协议已处理双向同步，无需额外延迟
        add_oren_service(settings_ref)
            .await
            .map_err(|e| InstallError::OrenOperationError(format!("添加服务失败: {}", e)))?;

        // 启动 OpenResty Edge Node - 传递 settings 参数
        // WebSocket 协议已处理双向同步，无需额外延迟
        start_oren_service(settings_ref)
            .await
            .map_err(|e| InstallError::OrenOperationError(format!("启动服务失败: {}", e)))?;

        // 完成 OpenResty Edge Node - 传递 settings 参数
        done_oren_service(settings_ref)
            .await
            .map_err(|e| InstallError::OrenOperationError(format!("完成服务失败: {}", e)))?;

        Ok::<(), InstallError>(())
    })?;

    // 询问用户是否需要启动服务
    let prompt_message = "是否立即启动服务?";
    let confirm = confirm_action(&prompt_message.yellow().to_string())?;

    if !confirm {
        info!("用户选择不启动服务, 正在停止服务...");
        // 停止服务
        stop_services("oren")
            .map_err(|e| InstallError::ServiceOperationError(format!("停止服务失败: {}", e)))?;
    } else {
        info!("用户选择保持服务运行");
    }

    // 打印安装成功的消息
    info!("安装成功!");
    println!("{}", "安装成功! Enjoy! 🥳".green());

    Ok(())
}
