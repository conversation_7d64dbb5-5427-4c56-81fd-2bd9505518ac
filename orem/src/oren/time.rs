use crate::basic::config::types::Settings;
use log::{debug, error, info};

use chrono::{DateTime, FixedOffset};
use rsntp::AsyncSntpClient;
use std::{process::Command, sync::Arc};
use thiserror::Error;

/// OREN 时间操作相关错误
#[derive(Error, Debug)]
pub enum OrenTimeError {
    /// 命令执行错误
    #[error("命令执行错误: {0}")]
    CommandExecutionError(String),

    /// NTP 同步错误
    #[error("NTP 同步错误: {0}")]
    NtpSyncError(String),

    /// 日期转换错误
    #[error("日期转换错误: {0}")]
    DateTimeConversionError(String),
}

/// 定义 Result 类型别名
pub type Result<T> = std::result::Result<T, OrenTimeError>;

/// 执行系统命令
pub fn execute_command(command: &str, args: &[&str], error_msg: &str) -> Result<()> {
    debug!("执行命令: {} {:?}", command, args);

    let output = Command::new(command).args(args).output().map_err(|e| {
        let err_msg = format!("{}: {:?}", error_msg, e);
        error!("{}", err_msg);
        OrenTimeError::CommandExecutionError(err_msg)
    })?;

    if !output.status.success() {
        let err_msg = format!("{}: {}", error_msg, String::from_utf8_lossy(&output.stderr));
        error!("{}", err_msg);
        return Err(OrenTimeError::CommandExecutionError(err_msg));
    }

    debug!("命令执行成功: {} {:?}", command, args);
    Ok(())
}

/// 从 NTP 服务器获取完整的当前日期时间 (UTC+8 时区)
pub async fn get_current_datetime_from_ntp(ntp_server: &str) -> Result<DateTime<FixedOffset>> {
    info!("从 NTP 服务器获取完整日期时间: {}", ntp_server);

    let client = AsyncSntpClient::new();

    let response = client.synchronize(ntp_server).await.map_err(|e| {
        let err_msg = format!("与 NTP 服务器同步失败: {:?}", e);
        error!("{}", err_msg);
        OrenTimeError::NtpSyncError(err_msg)
    })?;

    // 获取 UTC 时间
    let utc_datetime = response.datetime().into_chrono_datetime().map_err(|e| {
        let err_msg = format!("日期时间转换失败: {:?}", e);
        error!("{}", err_msg);
        OrenTimeError::DateTimeConversionError(err_msg)
    })?;

    // 转换为 UTC+8 时区 (东八区)
    let cst_offset = FixedOffset::east_opt(8 * 3600).unwrap(); // 8小时 = 8 * 3600秒
    let cst_datetime = utc_datetime.with_timezone(&cst_offset);

    info!(
        "从 NTP 获取到当前时间 (UTC+8): {}",
        cst_datetime.format("%Y-%m-%d %H:%M:%S %z")
    );

    Ok(cst_datetime)
}

/// 初始化 OpenResty Edge Node 时间设置
pub fn init_oren() -> Result<()> {
    info!("开始初始化时间...");

    // 关闭 NTP 同步
    execute_command("timedatectl", &["set-ntp", "off"], "关闭 NTP 同步失败")?;
    info!("已关闭 NTP 同步");

    // 获取当前时间并设置到 2023 年
    let current_time = chrono::Local::now().format("%H:%M:%S").to_string();
    let set_time_arg = format!("2023-05-01 {}", current_time);

    debug!("设置时间为: {}", set_time_arg);
    execute_command("timedatectl", &["set-time", &set_time_arg], "设置时间失败")?;
    info!("已设置初始时间: {}", set_time_arg);

    // 重启 OpenResty Edge Node
    execute_command(
        "systemctl",
        &["restart", "oredge-node"],
        "重启服务失败",
    )?;
    info!("已重启服务");

    Ok(())
}

/// 处理年份时间设定完成的客户端确认逻辑
///
/// 当服务端完成某年的时间设定后，客户端可以在此执行相应的本地操作
pub async fn handle_year_complete(year: i32) -> Result<()> {
    info!("收到年份 {} 处理完成通知，执行客户端确认逻辑", year);

    // 这里可以添加客户端需要在每年处理完成后执行的逻辑
    // 例如：检查本地服务状态、清理临时文件等

    debug!("年份 {} 的客户端确认逻辑执行完成", year);
    Ok(())
}

/// 处理启动完成的客户端确认逻辑
///
/// 当服务端完成启动操作后，客户端可以在此执行相应的本地操作
pub async fn handle_start_complete() -> Result<()> {
    info!("收到启动完成通知，执行客户端确认逻辑");

    // 这里可以添加客户端需要在启动完成后执行的逻辑
    // 例如：检查本地服务状态、验证配置等

    debug!("启动完成的客户端确认逻辑执行完成");
    Ok(())
}
