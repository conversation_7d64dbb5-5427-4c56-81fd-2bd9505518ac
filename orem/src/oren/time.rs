use log::{debug, error, info};

use chrono::{DateTime, FixedOffset};
use rsntp::AsyncSntpClient;
use std::process::Command;
use thiserror::Error;

/// OREN 时间操作相关错误
#[derive(Error, Debug)]
pub enum OrenTimeError {
    /// 命令执行错误
    #[error("命令执行错误: {0}")]
    CommandExecutionError(String),

    /// NTP 同步错误
    #[error("NTP 同步错误: {0}")]
    NtpSyncError(String),

    /// 日期转换错误
    #[error("日期转换错误: {0}")]
    DateTimeConversionError(String),
}

/// 定义 Result 类型别名
pub type Result<T> = std::result::Result<T, OrenTimeError>;

/// 执行系统命令
pub fn execute_command(command: &str, args: &[&str], error_msg: &str) -> Result<()> {
    debug!("执行命令: {} {:?}", command, args);

    let output = Command::new(command).args(args).output().map_err(|e| {
        let err_msg = format!("{}: {:?}", error_msg, e);
        error!("{}", err_msg);
        OrenTimeError::CommandExecutionError(err_msg)
    })?;

    if !output.status.success() {
        let err_msg = format!("{}: {}", error_msg, String::from_utf8_lossy(&output.stderr));
        error!("{}", err_msg);
        return Err(OrenTimeError::CommandExecutionError(err_msg));
    }

    debug!("命令执行成功: {} {:?}", command, args);
    Ok(())
}

/// 从 NTP 服务器获取完整的当前日期时间 (UTC+8 时区)
///
/// 注意: 此函数保留用于将来可能的扩展需求
#[allow(dead_code)]
pub async fn get_current_datetime_from_ntp(ntp_server: &str) -> Result<DateTime<FixedOffset>> {
    info!("从 NTP 服务器获取完整日期时间: {}", ntp_server);

    let client = AsyncSntpClient::new();

    let response = client.synchronize(ntp_server).await.map_err(|e| {
        let err_msg = format!("与 NTP 服务器同步失败: {:?}", e);
        error!("{}", err_msg);
        OrenTimeError::NtpSyncError(err_msg)
    })?;

    // 获取 UTC 时间
    let utc_datetime = response.datetime().into_chrono_datetime().map_err(|e| {
        let err_msg = format!("日期时间转换失败: {:?}", e);
        error!("{}", err_msg);
        OrenTimeError::DateTimeConversionError(err_msg)
    })?;

    // 转换为 UTC+8 时区 (东八区)
    let cst_offset = FixedOffset::east_opt(8 * 3600).unwrap(); // 8小时 = 8 * 3600秒
    let cst_datetime = utc_datetime.with_timezone(&cst_offset);

    info!(
        "从 NTP 获取到当前时间 (UTC+8): {}",
        cst_datetime.format("%Y-%m-%d %H:%M:%S %z")
    );

    Ok(cst_datetime)
}

/// 初始化 OpenResty Edge Node 时间设置
pub fn init_oren() -> Result<()> {
    info!("开始初始化时间...");

    // 关闭 NTP 同步
    execute_command("timedatectl", &["set-ntp", "off"], "关闭 NTP 同步失败")?;
    info!("已关闭 NTP 同步");

    // 获取当前时间并设置到 2023 年
    let current_time = chrono::Local::now().format("%H:%M:%S").to_string();
    let set_time_arg = format!("2023-05-01 {}", current_time);

    debug!("设置时间为: {}", set_time_arg);
    execute_command("timedatectl", &["set-time", &set_time_arg], "设置时间失败")?;
    info!("已设置初始时间: {}", set_time_arg);

    // 重启 OpenResty Edge Node
    execute_command(
        "systemctl",
        &["restart", "oredge-node"],
        "重启服务失败",
    )?;
    info!("已重启服务");

    Ok(())
}

/// 处理年份时间设定完成的客户端确认逻辑
///
/// 当服务端完成某年的时间设定后，客户端需要执行本地时间设定和服务重启
pub async fn handle_year_complete(year: i32) -> Result<()> {
    info!("收到年份 {} 处理完成通知，开始执行客户端本地操作", year);

    // 设置本地系统时间到指定年份
    set_local_time_to_year(year).await?;

    // 重启 OpenResty Edge Node 服务使时间生效
    restart_oren_service().await?;

    info!("年份 {} 的客户端本地操作执行完成", year);
    Ok(())
}

/// 设置本地系统时间到指定年份
async fn set_local_time_to_year(year: i32) -> Result<()> {
    info!("设置本地系统时间到 {} 年", year);

    // 关闭 NTP 同步
    execute_command("timedatectl", &["set-ntp", "off"], "关闭 NTP 同步失败")?;
    info!("已关闭 NTP 同步");

    // 获取当前时间并设置到指定年份
    let current_time = chrono::Local::now().format("%m-%d %H:%M:%S").to_string();
    let set_time_arg = format!("{}-{}", year, current_time);

    debug!("设置时间为: {}", set_time_arg);
    execute_command("timedatectl", &["set-time", &set_time_arg], "设置时间失败")?;
    info!("已设置时间到 {} 年: {}", year, set_time_arg);

    Ok(())
}

/// 重启 OpenResty Edge Node 服务
async fn restart_oren_service() -> Result<()> {
    info!("重启 OpenResty Edge Node 服务");

    execute_command(
        "systemctl",
        &["restart", "oredge-node"],
        "重启 OpenResty Edge Node 服务失败",
    )?;

    info!("OpenResty Edge Node 服务重启成功");
    Ok(())
}

/// 处理启动完成的客户端确认逻辑
///
/// 当服务端完成启动操作后，客户端需要执行本地启动相关操作
pub async fn handle_start_complete() -> Result<()> {
    info!("收到启动完成通知，开始执行客户端本地启动操作");

    // 重新开启 NTP 同步，确保时间准确性
    enable_ntp_sync().await?;

    // 等待 NTP 同步完成
    tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;

    // 重启 OpenResty Edge Node 服务确保配置生效
    restart_oren_service().await?;

    info!("启动完成的客户端本地操作执行完成");
    Ok(())
}

/// 启用 NTP 时间同步
async fn enable_ntp_sync() -> Result<()> {
    info!("启用 NTP 时间同步");

    execute_command("timedatectl", &["set-ntp", "on"], "启用 NTP 同步失败")?;
    info!("已启用 NTP 同步");

    Ok(())
}
